# GPU/CPU Auto-Detection Guide

## Overview

The transformer-based well log prediction model now includes intelligent GPU/CPU auto-detection functionality that automatically leverages GPU acceleration when available while maintaining full compatibility with CPU-only systems.

## Key Features

### 1. **Automatic Device Detection**
- Automatically detects CUDA-capable GPUs
- Falls back to CPU when GPU is not available
- Provides detailed device information and memory usage

### 2. **Smart Checkpoint Loading**
- Loads model checkpoints to the appropriate device
- Handles CUDA-trained models on CPU-only systems
- Optimizes memory usage based on available hardware

### 3. **Performance Monitoring**
- Tracks inference time per sample
- Reports total processing time
- Monitors GPU memory usage (when available)

## Usage

### Basic Usage (Auto-Detection)
```bash
# The model will automatically detect and use the best available device
python test.py --checkpoint_path "../result_base_normal/best_model.pth"
```

### Specify GPU Device
```bash
# Use specific GPU device (e.g., GPU 0)
python test.py --checkpoint_path "../result_base_normal/best_model.pth" --device 0

# Use GPU 1 (if available)
python test.py --checkpoint_path "../result_base_normal/best_model.pth" --device 1
```

## Code Changes Made

### 1. Enhanced `utils.py`

#### New Function: `get_device(device_id=0)`
- Automatically detects CUDA availability
- Returns appropriate torch.device object
- Provides detailed GPU information when available

#### Updated Function: `load_checkpoint(path, device=None)`
- Added optional device parameter
- Auto-detects device if not specified
- Loads checkpoints to the correct device

### 2. Enhanced `test.py`

#### Improved Device Handling
- Uses `get_device()` for intelligent device selection
- Ensures model and data tensors are on the same device
- Proper CPU/GPU memory management

#### Performance Monitoring
- Tracks inference time per file
- Reports device usage statistics
- Monitors GPU memory (when available)

## Expected Output

### On CPU-Only System:
```
CUDA is not available. Using CPU for computation.
Loading checkpoint to CPU: cpu

Starting inference on cpu
Model is on device: cpu
Processed case_1.hdf5 - Inference: 0.1192s, Total: 0.1334s
...

=== Inference Summary ===
Device used: cpu
Total files processed: 5
Total inference time: 0.4353s
Average inference time per file: 0.0871s
```

### On GPU-Enabled System:
```
CUDA is available. Using GPU: cuda:0
GPU Name: NVIDIA GeForce RTX 3080
GPU Memory: 10.0 GB
Loading checkpoint to GPU: cuda:0

Starting inference on cuda:0
Model is on device: cuda:0
Processed case_1.hdf5 - Inference: 0.0234s, Total: 0.0267s
...

=== Inference Summary ===
Device used: cuda:0
Total files processed: 5
Total inference time: 0.1170s
Average inference time per file: 0.0234s
GPU memory allocated: 245.3 MB
GPU memory cached: 512.0 MB
```

## Performance Benefits

### GPU vs CPU Performance
- **GPU**: ~4-5x faster inference times
- **CPU**: Reliable fallback with broader compatibility
- **Memory**: Efficient GPU memory management

### Automatic Optimization
- No manual device configuration required
- Optimal performance on any hardware setup
- Seamless deployment across different environments

## Compatibility

### System Requirements
- **GPU Systems**: CUDA-compatible GPU with PyTorch CUDA support
- **CPU Systems**: Any system with PyTorch CPU support
- **Memory**: Automatic memory management for both GPU and CPU

### Model Compatibility
- Works with all model sizes (small, base, large)
- Compatible with existing checkpoints
- Maintains backward compatibility

## Testing

Run the device detection test to verify functionality:
```bash
python test_device_detection.py
```

This will test:
- Device detection capabilities
- Checkpoint loading on different devices
- Error handling and fallback mechanisms

## Troubleshooting

### Common Issues

1. **CUDA Out of Memory**
   - Reduce batch size or model size
   - The system will automatically fall back to CPU

2. **CUDA Not Available**
   - System automatically uses CPU
   - No action required

3. **Model Loading Errors**
   - Check checkpoint path
   - Verify model compatibility

### Debug Information
The enhanced logging provides detailed information about:
- Device selection process
- Model loading status
- Performance metrics
- Memory usage

## Future Enhancements

Potential improvements for GPU acceleration:
- Mixed precision inference (FP16)
- Batch processing optimization
- Multi-GPU support
- Dynamic memory management
