"""
Analyze and visualize Vp prediction results
"""
import h5py
import numpy as np
import matplotlib.pyplot as plt
import os
from utils import cal_RMSE, cal_R2

def analyze_prediction_results(results_dir="../vp_prediction_results"):
    """
    Analyze the Vp prediction results
    """
    print("=== Vp Prediction Results Analysis ===")
    
    if not os.path.exists(results_dir):
        print(f"Results directory {results_dir} not found!")
        return
    
    # Find all result files
    result_files = [f for f in os.listdir(results_dir) if f.endswith('.hdf5')]
    
    if not result_files:
        print("No result files found!")
        return
    
    print(f"Found {len(result_files)} result files:")
    
    all_results = {}
    
    for result_file in result_files:
        file_path = os.path.join(results_dir, result_file)
        print(f"\nAnalyzing {result_file}:")
        
        with h5py.File(file_path, 'r') as f:
            # Load data
            real_vp = f['real_vp'][:]
            predicted_vp = f['predicted_vp'][:]
            input_features = f['input_features'][:]
            
            # Load metadata
            rmse = f.attrs.get('rmse', cal_RMSE(predicted_vp, real_vp))
            r2 = f.attrs.get('r2', cal_R2(predicted_vp, real_vp))
            source_file = f.attrs.get('source_file', 'unknown')
            model_type = f.attrs.get('model_type', 'unknown')
            inference_time = f.attrs.get('inference_time', 0)
            
            print(f"  Source: {source_file}")
            print(f"  Model: {model_type}")
            print(f"  RMSE: {rmse:.4f}")
            print(f"  R²: {r2:.4f}")
            print(f"  Inference time: {inference_time:.4f}s")
            print(f"  Real Vp range: [{real_vp.min():.2f}, {real_vp.max():.2f}]")
            print(f"  Predicted Vp range: [{predicted_vp.min():.2f}, {predicted_vp.max():.2f}]")
            
            # Store results
            all_results[result_file] = {
                'real_vp': real_vp,
                'predicted_vp': predicted_vp,
                'input_features': input_features,
                'rmse': rmse,
                'r2': r2,
                'source_file': source_file,
                'model_type': model_type,
                'inference_time': inference_time
            }
    
    # Create visualizations
    create_visualizations(all_results, results_dir)
    
    return all_results

def create_visualizations(results, output_dir):
    """
    Create visualization plots for the results
    """
    print(f"\nCreating visualizations...")
    
    n_files = len(results)
    if n_files == 0:
        return
    
    # Set up the plot
    fig, axes = plt.subplots(2, n_files, figsize=(5*n_files, 10))
    if n_files == 1:
        axes = axes.reshape(2, 1)
    
    for i, (filename, data) in enumerate(results.items()):
        real_vp = data['real_vp']
        predicted_vp = data['predicted_vp']
        rmse = data['rmse']
        r2 = data['r2']
        source = os.path.basename(data['source_file'])
        
        # Plot 1: Time series comparison
        ax1 = axes[0, i]
        depth_points = np.arange(len(real_vp))
        ax1.plot(depth_points, real_vp, 'b-', label='Real Vp', linewidth=1)
        ax1.plot(depth_points, predicted_vp, 'r-', label='Predicted Vp', linewidth=1)
        ax1.set_title(f'{source}\nRMSE: {rmse:.2f}, R²: {r2:.3f}')
        ax1.set_xlabel('Depth Points')
        ax1.set_ylabel('Vp (Sonic)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Plot 2: Scatter plot
        ax2 = axes[1, i]
        ax2.scatter(real_vp, predicted_vp, alpha=0.6, s=1)
        
        # Add perfect prediction line
        min_val = min(real_vp.min(), predicted_vp.min())
        max_val = max(real_vp.max(), predicted_vp.max())
        ax2.plot([min_val, max_val], [min_val, max_val], 'r--', label='Perfect Prediction')
        
        ax2.set_xlabel('Real Vp')
        ax2.set_ylabel('Predicted Vp')
        ax2.set_title(f'Prediction vs Reality\n{source}')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # Make axes equal
        ax2.set_aspect('equal', adjustable='box')
    
    plt.tight_layout()
    
    # Save the plot
    plot_path = os.path.join(output_dir, 'vp_prediction_analysis.png')
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    print(f"Saved visualization: {plot_path}")
    
    # Show the plot
    plt.show()

def create_detailed_analysis_report(results, output_dir):
    """
    Create a detailed analysis report
    """
    report_path = os.path.join(output_dir, 'vp_prediction_report.txt')
    
    with open(report_path, 'w') as f:
        f.write("=== Vp (Sonic Velocity) Prediction Analysis Report ===\n\n")
        f.write(f"Generated on: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Number of files analyzed: {len(results)}\n\n")
        
        # Summary statistics
        all_rmse = [data['rmse'] for data in results.values()]
        all_r2 = [data['r2'] for data in results.values()]
        all_inference_times = [data['inference_time'] for data in results.values()]
        
        f.write("=== Summary Statistics ===\n")
        f.write(f"Average RMSE: {np.mean(all_rmse):.4f} ± {np.std(all_rmse):.4f}\n")
        f.write(f"Average R²: {np.mean(all_r2):.4f} ± {np.std(all_r2):.4f}\n")
        f.write(f"Average inference time: {np.mean(all_inference_times):.4f}s ± {np.std(all_inference_times):.4f}s\n\n")
        
        # Individual file results
        f.write("=== Individual File Results ===\n")
        for filename, data in results.items():
            f.write(f"\nFile: {filename}\n")
            f.write(f"  Source: {data['source_file']}\n")
            f.write(f"  Model: {data['model_type']}\n")
            f.write(f"  RMSE: {data['rmse']:.4f}\n")
            f.write(f"  R²: {data['r2']:.4f}\n")
            f.write(f"  Inference time: {data['inference_time']:.4f}s\n")
            f.write(f"  Real Vp range: [{data['real_vp'].min():.2f}, {data['real_vp'].max():.2f}]\n")
            f.write(f"  Predicted Vp range: [{data['predicted_vp'].min():.2f}, {data['predicted_vp'].max():.2f}]\n")
        
        # Analysis and recommendations
        f.write("\n=== Analysis and Recommendations ===\n")
        f.write("1. Model Performance:\n")
        if np.mean(all_r2) < 0:
            f.write("   - The current model shows poor performance for Vp prediction (negative R²)\n")
            f.write("   - This is expected as the model was trained for density prediction, not sonic\n")
        
        f.write("\n2. Recommendations for Improvement:\n")
        f.write("   - Train a dedicated model for Vp prediction using AC as target\n")
        f.write("   - Use GR, CNL, DEN, RLLD as input features for Vp prediction\n")
        f.write("   - Consider data normalization and feature scaling\n")
        f.write("   - Evaluate different model architectures (small, base, large)\n")
        
        f.write("\n3. Data Insights:\n")
        f.write("   - A1.hdf5 and A2.hdf5 contain real well log data\n")
        f.write("   - Synthetic data shows different characteristics\n")
        f.write("   - Consider domain-specific preprocessing for better results\n")
    
    print(f"Saved detailed report: {report_path}")

def compare_with_baseline(results):
    """
    Compare results with simple baseline predictions
    """
    print("\n=== Baseline Comparison ===")
    
    for filename, data in results.items():
        real_vp = data['real_vp']
        predicted_vp = data['predicted_vp']
        
        # Simple baselines
        mean_baseline = np.full_like(real_vp, np.mean(real_vp))
        median_baseline = np.full_like(real_vp, np.median(real_vp))
        
        # Calculate metrics for baselines
        rmse_mean = cal_RMSE(mean_baseline, real_vp)
        rmse_median = cal_RMSE(median_baseline, real_vp)
        rmse_model = data['rmse']
        
        r2_mean = cal_R2(mean_baseline, real_vp)
        r2_median = cal_R2(median_baseline, real_vp)
        r2_model = data['r2']
        
        print(f"\n{filename}:")
        print(f"  Model RMSE: {rmse_model:.4f}, R²: {r2_model:.4f}")
        print(f"  Mean baseline RMSE: {rmse_mean:.4f}, R²: {r2_mean:.4f}")
        print(f"  Median baseline RMSE: {rmse_median:.4f}, R²: {r2_median:.4f}")
        
        if rmse_model < rmse_mean:
            print(f"  ✓ Model outperforms mean baseline")
        else:
            print(f"  ✗ Model underperforms mean baseline")

if __name__ == "__main__":
    import time
    
    # Analyze results
    results = analyze_prediction_results()
    
    if results:
        # Create detailed report
        create_detailed_analysis_report(results, "../vp_prediction_results")
        
        # Compare with baselines
        compare_with_baseline(results)
        
        print("\n=== Analysis Complete ===")
        print("Check the following files for detailed results:")
        print("  - vp_prediction_analysis.png (visualization)")
        print("  - vp_prediction_report.txt (detailed report)")
    else:
        print("No results to analyze. Run test_vp_prediction.py first.")
