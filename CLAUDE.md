# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Machine Learning Well Log Transformer (MWLT) project for predicting well log properties using transformer-based deep learning models. The system primarily focuses on density (DEN) prediction from input curves (GR, AC, CNL, RLLD) and includes experimental support for sonic velocity (Vp/AC) prediction.

## Core Architecture

### Model Components (code/model.py)
- **Input_Embedding**: ResNet-based feature extraction with 1D convolutions
- **MWLT Models**: Three transformer variants (Small, Base, Large) with different capacities:
  - Small: 2 ResNet blocks, 2 transformer encoders, 2 attention heads, 64 features
  - Base: 4 ResNet blocks, 4 transformer encoders, 4 attention heads, 64 features  
  - Large: 6 ResNet blocks, 6 transformer encoders, 8 attention heads, 128 features
- **Architecture**: Input curves → ResNet feature extraction → Transformer encoders → Decoder → Output curves

### Data Pipeline (code/dataset.py)
- **WellDataset**: Handles HDF5 well log files with sliding window approach
- **Input/Output**: 4 input curves → 1-3 output curves, sequence length 720→640 with augmentation
- **Data Format**: HDF5 files containing curve arrays (GR, AC, CNL, RLLD, DEN)

## Development Commands

### Training Models
```bash
# Main density prediction training
cd code
python train.py --model_type base --epochs 2000 --batch_size 64 --save_path ../result_base_normal

# Quick model testing (Windows)
run_test.bat

# Sonic velocity (Vp) prediction training
python simple_train_vp.py                    # Quick test (30 epochs)
python train_vp_model.py --model_type base   # Full training
python train_vp_transfer.py --pretrained_model_path ../result_base_normal/best_model.pth
```

### Testing and Evaluation
```bash
# Test trained models
python test.py --test_files_path="../data_normal/val" --checkpoint_path="../result_base_normal/best_model.pth"

# Device detection testing
python test_device_detection.py

# Vp prediction testing
python test_vp_prediction.py --checkpoint_path ../simple_vp_training/best_simple_vp_model.pth
```

### Data Processing
```bash
# LAS file processing
python las_processor.py

# Prepare Vp training data
python prepare_vp_data.py

# Create sample data
python create_sample_data.py
```

## Directory Structure

### Data Organization
- `data_normal/`: Normalized training data (train/val splits)
- `data_nonormal/`: Non-normalized training data
- `data_vp_prediction/`: Prepared data for sonic velocity prediction
- `las_test_data/`: Sample LAS and HDF5 well files
- `simple_vp_data/`: Simple Vp training datasets

### Model Results
- `result_base_normal/`: Base model results with normalization
- `result_*_*/`: Different model size and normalization combinations
- `simple_vp_training/`: Vp prediction model results
- `vp_prediction_results/`: Final Vp prediction outputs

### Code Structure
- `model.py`: Core transformer architecture
- `train.py`: Main training script
- `test.py`: Model evaluation and inference
- `dataset.py`: Data loading and preprocessing
- `utils.py`: Utilities including GPU/CPU auto-detection
- `*vp*.py`: Sonic velocity prediction specialized scripts

## Key Features

### GPU/CPU Auto-Detection
The system automatically detects and uses available hardware:
- `utils.get_device()`: Intelligent device selection with GPU info
- `utils.load_checkpoint()`: Device-aware model loading
- Automatic fallback to CPU when GPU unavailable

### Model Training Configurations
- **Input curves**: GR (Gamma Ray), AC (Acoustic), CNL (Neutron), RLLD (Resistivity)
- **Output curves**: DEN (Density) for main models, AC for Vp prediction
- **Sequence handling**: 720-point windows with 640-point effective sequences
- **Data augmentation**: Random cropping and normalization options

### Hyperparameter Defaults
- Learning rate: 1e-5 (density), 1e-4 (Vp from scratch)
- Batch size: 64 (density), 16 (Vp)
- Patience: 150 epochs (density), 100 epochs (Vp)
- Dropout rates: 0.1 across model components

## Well Log Domain Context

### Input Curve Meanings
- **GR**: Gamma Ray (radioactivity, shale content indicator)
- **AC**: Acoustic/Sonic (travel time, porosity/lithology indicator)  
- **CNL**: Neutron (hydrogen content, porosity indicator)
- **RLLD**: Deep resistivity (fluid saturation indicator)
- **DEN**: Density (bulk density, lithology/porosity indicator)

### Model Applications
- **Primary**: Density curve prediction from other logs
- **Experimental**: Sonic velocity prediction for seismic applications
- **Use case**: Fill missing log sections, quality control, synthetic log generation

## Development Notes

### Testing Approach
- No standardized test framework - uses custom evaluation scripts
- Model evaluation through RMSE, R², and visual comparison
- Performance monitoring via training loss curves and validation metrics

### Data Requirements
- Well log data in HDF5 format with standardized curve names
- Minimum sequence length of 720 points for effective training
- Proper depth registration and quality control of input curves

### Performance Expectations
- **GPU acceleration**: 4-5x faster than CPU inference
- **Model accuracy**: RMSE <0.1 for density prediction on normalized data
- **Training time**: Hours to days depending on model size and data volume