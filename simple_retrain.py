#!/usr/bin/env python3
"""
Simplified retraining script for VpTransformer model
This script will retrain the model with the corrected state saving
"""
import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import time

# Add code directory to path
sys.path.append('code')

def main():
    print("🚀 Starting VpTransformer Model Retraining")
    print("=" * 60)
    
    # Step 1: Import required modules
    print("📦 Importing modules...")
    try:
        from vp_model_improved import MWLT_Vp_Base, VpDataNormalizer, VpLoss
        from utils import get_device, EarlyStopping, cal_RMSE, cal_R2
        from las_processor import LASProcessor
        print("✅ All modules imported successfully")
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False
    
    # Step 2: Setup device
    print("\n🖥️  Setting up device...")
    try:
        device = get_device(0)
        print(f"✅ Using device: {device}")
    except Exception as e:
        print(f"❌ Device setup error: {e}")
        return False
    
    # Step 3: Find data files
    print("\n📁 Looking for data files...")
    data_files = []
    possible_paths = ['.', '..']
    
    for base_path in possible_paths:
        a1_path = os.path.join(base_path, 'A1.hdf5')
        a2_path = os.path.join(base_path, 'A2.hdf5')
        
        if os.path.exists(a1_path) and os.path.exists(a2_path):
            data_files = [a1_path, a2_path]
            print(f"✅ Found data files in: {os.path.abspath(base_path)}")
            break
    
    if not data_files:
        print("❌ Could not find A1.hdf5 and A2.hdf5 files!")
        return False
    
    # Step 4: Create training data
    print("\n🔄 Creating training data...")
    try:
        processor = LASProcessor()
        normalizer = VpDataNormalizer()
        
        all_inputs = []
        all_targets = []
        
        for file_path in data_files:
            print(f"   Processing {os.path.basename(file_path)}...")
            curves = processor.process_hdf5_to_las_format(file_path)
            
            # Create training samples
            sequence_length = 640
            data_length = len(curves['AC'])
            
            if data_length >= sequence_length:
                # Extract input features
                input_features = []
                for curve_name in ['GR', 'CNL', 'DEN', 'RLLD']:
                    if curve_name in curves:
                        data = curves[curve_name][:sequence_length]
                        if curve_name == 'RLLD':
                            # Log transform for resistivity
                            data = np.log10(np.clip(data, 0.1, None))
                            normalized = (data - 1.0) / 2.0
                        else:
                            stats = normalizer.input_stats[curve_name]
                            normalized = (data - stats['mean']) / stats['std']
                            normalized = np.clip(normalized, -3, 3) / 3
                        input_features.append(normalized)
                    else:
                        input_features.append(np.zeros(sequence_length))
                
                # Target (AC curve)
                target = curves['AC'][:sequence_length]
                
                all_inputs.append(np.array(input_features))
                all_targets.append(target)
        
        input_data = np.array(all_inputs)
        target_data = np.array(all_targets)
        
        print(f"✅ Created training data: {input_data.shape} inputs, {target_data.shape} targets")
        
    except Exception as e:
        print(f"❌ Data creation error: {e}")
        return False
    
    # Step 5: Create model
    print("\n🧠 Creating model...")
    try:
        model = MWLT_Vp_Base()
        model = model.to(device)
        
        param_count = sum(p.numel() for p in model.parameters())
        print(f"✅ Model created with {param_count:,} parameters")
    except Exception as e:
        print(f"❌ Model creation error: {e}")
        return False
    
    # Step 6: Setup training
    print("\n⚙️  Setting up training...")
    try:
        optimizer = optim.Adam(model.parameters(), lr=1e-4)
        criterion = VpLoss()
        
        # Create output directory
        save_path = os.path.join('..', 'vp_improved_training')
        os.makedirs(save_path, exist_ok=True)
        
        early_stopping = EarlyStopping(
            patience=20,
            path=os.path.join(save_path, 'best_vp_improved_model.pth'),
            verbose=True
        )
        
        print("✅ Training setup complete")
    except Exception as e:
        print(f"❌ Training setup error: {e}")
        return False
    
    # Step 7: Training loop
    print("\n🏃 Starting training...")
    print(f"   Epochs: 50 (reduced for quick retraining)")
    print(f"   Device: {device}")
    print(f"   Samples: {len(input_data)}")
    print("-" * 60)
    
    try:
        # Convert to tensors
        input_tensor = torch.FloatTensor(input_data).to(device)
        target_tensor = torch.FloatTensor(target_data).unsqueeze(1).to(device)
        
        for epoch in range(1, 51):  # 50 epochs for quick retraining
            start_time = time.time()
            
            # Training
            model.train()
            optimizer.zero_grad()
            
            outputs = model(input_tensor)
            loss = criterion(outputs, target_tensor)
            
            loss.backward()
            optimizer.step()
            
            # Validation (using same data for simplicity)
            model.eval()
            with torch.no_grad():
                val_outputs = model(input_tensor)
                val_loss = criterion(val_outputs, target_tensor).item()
                
                # Calculate metrics
                predictions = val_outputs.cpu().numpy().flatten()
                actuals = target_tensor.cpu().numpy().flatten()
                rmse = cal_RMSE(predictions, actuals)
                r2 = cal_R2(predictions, actuals)
            
            epoch_time = time.time() - start_time
            
            print(f"Epoch {epoch:3d}: Loss: {val_loss:.6f}, RMSE: {rmse:.2f}, R²: {r2:.4f}, Time: {epoch_time:.1f}s")
            
            # Save checkpoint with CORRECTED state structure
            state = {
                "model_state_dict": model.state_dict(),
                "optimizer_state_dict": optimizer.state_dict(),
                "loss": val_loss,
                "epoch": epoch,
                "rmse": rmse,
                "r2": r2
            }
            
            early_stopping(state, model)
            
            if early_stopping.early_stop:
                print(f"Early stopping at epoch {epoch}")
                break
        
        print("\n✅ Training completed successfully!")
        print(f"📁 Model saved to: {os.path.join(save_path, 'best_vp_improved_model.pth')}")
        
    except Exception as e:
        print(f"❌ Training error: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 Retraining completed successfully!")
        print("   The model now contains the proper 'model_state_dict' structure.")
        print("   You can now test the model loading with:")
        print("   python code/test_vp_improved.py")
    else:
        print("\n❌ Retraining failed. Please check the errors above.")
    
    sys.exit(0 if success else 1)
