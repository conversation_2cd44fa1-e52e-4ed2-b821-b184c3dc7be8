#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to inspect the saved VpTransformer model and diagnose the loading issue
"""
import torch
import os
import sys

def inspect_saved_model(model_path):
    """Inspect the structure of the saved model"""
    print(f"=== Inspecting Model: {model_path} ===")
    
    if not os.path.exists(model_path):
        print(f"❌ Model file not found: {model_path}")
        return None
    
    try:
        # Load with weights_only=False to see the full structure
        checkpoint = torch.load(model_path, map_location='cpu', weights_only=False)
        print(f"✅ Model loaded successfully")
        print(f"File size: {os.path.getsize(model_path) / (1024*1024):.2f} MB")
        
        if isinstance(checkpoint, dict):
            print(f"\n📋 Checkpoint is a dictionary with {len(checkpoint)} keys:")
            for key, value in checkpoint.items():
                print(f"  {key}: {type(value)}")
                if key == 'model_state_dict' and isinstance(value, dict):
                    print(f"    └─ Contains {len(value)} model parameters")
                elif isinstance(value, (int, float, str)):
                    print(f"    └─ Value: {value}")
        else:
            print(f"\n📋 Checkpoint is a {type(checkpoint)}")
            if hasattr(checkpoint, 'keys'):
                print(f"Contains {len(checkpoint)} keys:")
                for i, key in enumerate(list(checkpoint.keys())[:20]):
                    value = checkpoint[key]
                    shape_info = f" {value.shape}" if hasattr(value, 'shape') else ""
                    print(f"  {key}: {type(value)}{shape_info}")
                if len(checkpoint) > 20:
                    print(f"  ... and {len(checkpoint) - 20} more keys")
        
        return checkpoint
        
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return None

def inspect_model_architecture():
    """Inspect the expected VpTransformer architecture"""
    print(f"\n=== Expected VpTransformer Architecture ===")
    
    try:
        # Import the model with proper path handling
        import os
        code_path = os.path.join(os.path.dirname(__file__), 'code')
        if code_path not in sys.path:
            sys.path.insert(0, code_path)

        from vp_model_improved import MWLT_Vp_Base
        
        # Create a model instance
        model = MWLT_Vp_Base()
        state_dict = model.state_dict()
        
        print(f"✅ VpTransformer model created successfully")
        print(f"📋 Expected state_dict has {len(state_dict)} parameters:")
        
        # Group parameters by component
        components = {}
        for key in state_dict.keys():
            component = key.split('.')[0]
            if component not in components:
                components[component] = []
            components[component].append(key)
        
        for component, params in components.items():
            print(f"\n  {component}: ({len(params)} parameters)")
            for param in params[:5]:  # Show first 5 parameters
                shape = state_dict[param].shape
                print(f"    {param}: {shape}")
            if len(params) > 5:
                print(f"    ... and {len(params) - 5} more parameters")
        
        return state_dict
        
    except Exception as e:
        print(f"❌ Error creating model: {e}")
        return None

def compare_model_structures(saved_checkpoint, expected_state_dict):
    """Compare saved model with expected architecture"""
    print(f"\n=== Comparing Model Structures ===")
    
    if saved_checkpoint is None or expected_state_dict is None:
        print("❌ Cannot compare - missing data")
        return
    
    # Extract model state dict from checkpoint
    if isinstance(saved_checkpoint, dict) and 'model_state_dict' in saved_checkpoint:
        saved_state_dict = saved_checkpoint['model_state_dict']
    elif hasattr(saved_checkpoint, 'keys'):
        saved_state_dict = saved_checkpoint
    else:
        print("❌ Cannot extract state_dict from saved checkpoint")
        return
    
    print(f"📊 Saved model: {len(saved_state_dict)} parameters")
    print(f"📊 Expected model: {len(expected_state_dict)} parameters")
    
    # Find missing and unexpected keys
    saved_keys = set(saved_state_dict.keys())
    expected_keys = set(expected_state_dict.keys())
    
    missing_keys = expected_keys - saved_keys
    unexpected_keys = saved_keys - expected_keys
    
    if missing_keys:
        print(f"\n❌ Missing keys ({len(missing_keys)}):")
        for key in sorted(list(missing_keys)[:10]):
            print(f"  {key}")
        if len(missing_keys) > 10:
            print(f"  ... and {len(missing_keys) - 10} more")
    
    if unexpected_keys:
        print(f"\n⚠️  Unexpected keys ({len(unexpected_keys)}):")
        for key in sorted(list(unexpected_keys)[:10]):
            print(f"  {key}")
        if len(unexpected_keys) > 10:
            print(f"  ... and {len(unexpected_keys) - 10} more")
    
    # Check shape mismatches for common keys
    common_keys = saved_keys & expected_keys
    shape_mismatches = []
    
    for key in common_keys:
        saved_shape = saved_state_dict[key].shape
        expected_shape = expected_state_dict[key].shape
        if saved_shape != expected_shape:
            shape_mismatches.append((key, saved_shape, expected_shape))
    
    if shape_mismatches:
        print(f"\n⚠️  Shape mismatches ({len(shape_mismatches)}):")
        for key, saved_shape, expected_shape in shape_mismatches[:5]:
            print(f"  {key}: saved {saved_shape} vs expected {expected_shape}")
        if len(shape_mismatches) > 5:
            print(f"  ... and {len(shape_mismatches) - 5} more")
    
    if not missing_keys and not unexpected_keys and not shape_mismatches:
        print("✅ Model structures match perfectly!")

def main():
    """Main inspection function"""
    model_path = '../vp_improved_training/best_vp_improved_model.pth'
    
    # Inspect saved model
    saved_checkpoint = inspect_saved_model(model_path)
    
    # Inspect expected architecture
    expected_state_dict = inspect_model_architecture()
    
    # Compare structures
    compare_model_structures(saved_checkpoint, expected_state_dict)
    
    print(f"\n=== Recommendations ===")
    if saved_checkpoint is not None and expected_state_dict is not None:
        print("1. Check if the model was saved correctly during training")
        print("2. Verify that the same model architecture is being used")
        print("3. Consider using torch.load with weights_only=True for security")
        print("4. Update the loading code to handle the checkpoint structure properly")

if __name__ == "__main__":
    main()
