#!/usr/bin/env python3
"""
Test script to verify training setup before running full training
"""
import os
import sys
import torch

# Add code directory to path
sys.path.append('code')

def test_imports():
    """Test if all required modules can be imported"""
    print("=== Testing Imports ===")
    try:
        from train_vp_improved import find_data_files, create_improved_vp_data
        print("✅ train_vp_improved imports successful")
        
        from vp_model_improved import MWLT_Vp_Base, VpDataNormalizer, VpLoss
        print("✅ vp_model_improved imports successful")
        
        from utils import get_device, EarlyStopping, cal_RMSE, cal_R2
        print("✅ utils imports successful")
        
        from las_processor import LASProcessor
        print("✅ las_processor imports successful")
        
        return True
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_data_files():
    """Test if data files can be found"""
    print("\n=== Testing Data Files ===")
    try:
        sys.path.append('code')
        from train_vp_improved import find_data_files
        
        files = find_data_files()
        if files:
            print(f"✅ Found {len(files)} data files")
            for f in files:
                size_mb = os.path.getsize(f) / (1024*1024)
                print(f"   {f} ({size_mb:.1f} MB)")
            return True
        else:
            print("❌ No data files found")
            return False
    except Exception as e:
        print(f"❌ Data file error: {e}")
        return False

def test_device():
    """Test device detection"""
    print("\n=== Testing Device ===")
    try:
        from utils import get_device
        device = get_device(0)
        print(f"✅ Device: {device}")
        print(f"   CUDA available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"   CUDA device count: {torch.cuda.device_count()}")
            print(f"   Current device: {torch.cuda.current_device()}")
        return True
    except Exception as e:
        print(f"❌ Device error: {e}")
        return False

def test_model_creation():
    """Test if model can be created"""
    print("\n=== Testing Model Creation ===")
    try:
        from vp_model_improved import MWLT_Vp_Base
        model = MWLT_Vp_Base()
        param_count = sum(p.numel() for p in model.parameters())
        print(f"✅ Model created successfully")
        print(f"   Parameters: {param_count:,}")
        return True
    except Exception as e:
        print(f"❌ Model creation error: {e}")
        return False

def test_data_creation():
    """Test if training data can be created"""
    print("\n=== Testing Data Creation ===")
    try:
        from train_vp_improved import create_improved_vp_data
        print("Creating training data (this may take a moment)...")
        input_data, target_data = create_improved_vp_data()
        
        if len(input_data) > 0:
            print(f"✅ Training data created successfully")
            print(f"   Input shape: {input_data.shape}")
            print(f"   Target shape: {target_data.shape}")
            print(f"   Samples: {len(input_data)}")
            return True
        else:
            print("❌ No training data created")
            return False
    except Exception as e:
        print(f"❌ Data creation error: {e}")
        return False

def main():
    """Run all tests"""
    print("🔍 Testing VpTransformer Training Setup")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_data_files,
        test_device,
        test_model_creation,
        test_data_creation
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("🏁 Test Summary")
    print("=" * 50)
    
    test_names = [
        "Imports",
        "Data Files", 
        "Device Detection",
        "Model Creation",
        "Data Creation"
    ]
    
    all_passed = True
    for name, result in zip(test_names, results):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{name:15}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All tests passed! Ready to start training.")
        return True
    else:
        print("\n⚠️  Some tests failed. Please fix issues before training.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
