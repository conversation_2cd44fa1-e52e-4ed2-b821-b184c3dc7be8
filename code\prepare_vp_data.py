"""
Prepare A1.hdf5 and A2.hdf5 data for Vp (Sonic) prediction
Converts the data format to be compatible with the transformer model
"""
import h5py
import numpy as np
import os
from las_processor import LASProcessor

def prepare_vp_prediction_data():
    """
    Prepare A1.hdf5 and A2.hdf5 for Vp prediction
    """
    processor = LASProcessor()
    
    # Create output directory
    output_dir = "../data_vp_prediction"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # Process A1.hdf5 and A2.hdf5
    input_files = ['../A1.hdf5', '../A2.hdf5']
    
    for input_file in input_files:
        if not os.path.exists(input_file):
            print(f"Warning: {input_file} not found, skipping...")
            continue
            
        print(f"\nProcessing {input_file}...")
        
        # Read original data
        with h5py.File(input_file, 'r') as f:
            print(f"Original data keys: {list(f.keys())}")
            
            # Extract all curves
            curves = {}
            for key in f.keys():
                data = f[key][:]
                if data.ndim > 1:
                    data = data.squeeze()
                curves[key] = data
                print(f"  {key}: shape {data.shape}, range [{data.min():.2f}, {data.max():.2f}]")
        
        # Prepare data for Vp prediction (AC as target)
        target_length = 720
        input_features, target_ac = processor.prepare_for_prediction(curves, target_length)
        
        print(f"Prepared data:")
        print(f"  Input features shape: {input_features.shape}")
        print(f"  Target (AC) shape: {target_ac.shape}")
        
        # Create output file for transformer model
        base_name = os.path.basename(input_file).replace('.hdf5', '')
        output_file = os.path.join(output_dir, f"{base_name}_vp_ready.hdf5")
        
        with h5py.File(output_file, 'w') as f:
            # Save input curves (GR, CNL, DEN, RLLD)
            curve_names = ['GR', 'CNL', 'DEN', 'RLLD']
            for i, curve_name in enumerate(curve_names):
                f.create_dataset(curve_name, data=input_features[i].astype(np.float32))
            
            # Save target (AC as Vp)
            f.create_dataset('AC', data=target_ac.squeeze().astype(np.float32))
            
            # Add metadata
            f.attrs['source_file'] = input_file
            f.attrs['target_curve'] = 'AC'
            f.attrs['input_curves'] = curve_names
            f.attrs['sequence_length'] = target_length
            f.attrs['description'] = 'Data prepared for Vp (sonic velocity) prediction'
        
        print(f"Saved: {output_file}")
        
        # Also create a validation version with some data points removed for testing
        val_output_file = os.path.join(output_dir, f"{base_name}_vp_val.hdf5")
        
        # Create validation data by masking some AC values
        ac_val = target_ac.copy()
        mask_indices = np.random.choice(target_length, size=target_length//4, replace=False)
        ac_val[0, mask_indices] = np.nan  # Mask some values for prediction testing
        
        with h5py.File(val_output_file, 'w') as f:
            # Save input curves
            for i, curve_name in enumerate(curve_names):
                f.create_dataset(curve_name, data=input_features[i].astype(np.float32))
            
            # Save masked target
            f.create_dataset('AC', data=ac_val.squeeze().astype(np.float32))
            f.create_dataset('AC_original', data=target_ac.squeeze().astype(np.float32))
            f.create_dataset('mask_indices', data=mask_indices)
            
            # Add metadata
            f.attrs['source_file'] = input_file
            f.attrs['target_curve'] = 'AC'
            f.attrs['input_curves'] = curve_names
            f.attrs['sequence_length'] = target_length
            f.attrs['masked_points'] = len(mask_indices)
            f.attrs['description'] = 'Validation data with masked AC values for prediction testing'
        
        print(f"Saved validation data: {val_output_file}")

def create_synthetic_las_data():
    """
    Create synthetic LAS files for testing
    """
    processor = LASProcessor()
    
    # Create output directory
    output_dir = "../las_test_data"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # Create multiple synthetic wells
    well_names = ['WELL_001', 'WELL_002', 'WELL_003']
    
    for well_name in well_names:
        print(f"\nCreating synthetic LAS file: {well_name}")
        
        # Generate synthetic data
        curves = processor._simple_las_reader("")
        
        # Add some variation for each well
        np.random.seed(hash(well_name) % 1000)  # Reproducible but different for each well
        
        # Modify curves slightly for variation
        curves['GR'] += np.random.normal(0, 5, len(curves['GR']))
        curves['CNL'] += np.random.normal(0, 2, len(curves['CNL']))
        curves['DEN'] += np.random.normal(0, 0.05, len(curves['DEN']))
        curves['RLLD'] *= np.random.lognormal(0, 0.1, len(curves['RLLD']))
        curves['AC'] += np.random.normal(0, 3, len(curves['AC']))
        
        # Apply realistic ranges again
        curves['GR'] = np.clip(curves['GR'], 0, 200)
        curves['CNL'] = np.clip(curves['CNL'], 0, 60)
        curves['DEN'] = np.clip(curves['DEN'], 1.5, 3.0)
        curves['RLLD'] = np.clip(curves['RLLD'], 0.1, 1000)
        curves['AC'] = np.clip(curves['AC'], 40, 140)
        
        # Save as LAS file
        las_file = os.path.join(output_dir, f"{well_name}.las")
        processor._save_as_las(curves, las_file)
        
        # Also save as HDF5 for comparison
        hdf5_file = os.path.join(output_dir, f"{well_name}.hdf5")
        with h5py.File(hdf5_file, 'w') as f:
            for curve_name, data in curves.items():
                f.create_dataset(curve_name, data=data.astype(np.float32))
            
            f.attrs['well_name'] = well_name
            f.attrs['data_type'] = 'synthetic'
            f.attrs['description'] = 'Synthetic well log data for testing'
        
        print(f"  Created: {las_file}")
        print(f"  Created: {hdf5_file}")

def analyze_data_statistics():
    """
    Analyze statistics of A1.hdf5 and A2.hdf5 for model training insights
    """
    print("\n=== Data Statistics Analysis ===")
    
    files = ['../A1.hdf5', '../A2.hdf5']
    all_stats = {}
    
    for file_path in files:
        if not os.path.exists(file_path):
            continue
            
        print(f"\nAnalyzing {file_path}:")
        file_stats = {}
        
        with h5py.File(file_path, 'r') as f:
            for curve_name in f.keys():
                data = f[curve_name][:].squeeze()
                
                stats = {
                    'mean': np.mean(data),
                    'std': np.std(data),
                    'min': np.min(data),
                    'max': np.max(data),
                    'median': np.median(data),
                    'length': len(data)
                }
                
                file_stats[curve_name] = stats
                print(f"  {curve_name:6s}: mean={stats['mean']:8.2f}, std={stats['std']:6.2f}, "
                      f"range=[{stats['min']:6.2f}, {stats['max']:6.2f}], len={stats['length']}")
        
        all_stats[file_path] = file_stats
    
    # Compare statistics between files
    if len(all_stats) > 1:
        print(f"\n=== Comparison between files ===")
        curves = set()
        for file_stats in all_stats.values():
            curves.update(file_stats.keys())
        
        for curve in sorted(curves):
            print(f"\n{curve} comparison:")
            for file_path, file_stats in all_stats.items():
                if curve in file_stats:
                    stats = file_stats[curve]
                    print(f"  {os.path.basename(file_path):10s}: "
                          f"mean={stats['mean']:8.2f}, std={stats['std']:6.2f}")

if __name__ == "__main__":
    print("=== Vp Prediction Data Preparation ===")
    
    # Prepare A1.hdf5 and A2.hdf5 for Vp prediction
    prepare_vp_prediction_data()
    
    # Create synthetic LAS data for testing
    create_synthetic_las_data()
    
    # Analyze data statistics
    analyze_data_statistics()
    
    print("\n=== Data preparation completed! ===")
    print("You can now run Vp prediction using:")
    print("  python test_vp_prediction.py")
    print("  python test_vp_prediction.py --input_file ../las_test_data/WELL_001.hdf5")
