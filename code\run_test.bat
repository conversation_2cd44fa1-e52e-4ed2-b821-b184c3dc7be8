@echo off
setlocal enabledelayedexpansion
echo ========================================
echo    MWLT Transformer Test Suite
echo ========================================
echo Current directory: %CD%

REM Change to the code directory
cd /d "%~dp0"

echo.
echo Available Test Options:
echo 1. Standard Density Prediction Test with Plotting (ENHANCED!)
echo 2. Vp (Sonic Velocity) Prediction Test (Legacy)
echo 3. Improved Vp Prediction with Plotting (NEW!)
echo 4. Device Detection Test
echo 5. Quick Vp Training Test
echo 6. Train Improved Vp Model
echo 7. Exit
echo.

set /p choice="Enter your choice (1-7): "

if "%choice%"=="1" goto standard_test
if "%choice%"=="2" goto vp_test
if "%choice%"=="3" goto vp_improved_test
if "%choice%"=="4" goto device_test
if "%choice%"=="5" goto quick_training
if "%choice%"=="6" goto train_improved
if "%choice%"=="7" goto exit
echo Invalid choice. Running standard test...

:standard_test
echo.
echo ========================================
echo Running Standard Density Prediction Test with Plotting
echo ========================================
echo Input: GR, AC, CNL, RLLD
echo Output: DEN (Density)
echo Model: Base model trained for density prediction
echo Features: Enhanced visualization similar to Vp plotting
echo.

REM Check if model exists
if not exist "..\result_base_normal\best_model.pth" (
    echo ❌ Model not found: ..\result_base_normal\best_model.pth
    echo Please ensure the density prediction model is trained and available.
    goto end
)

echo Running density prediction...
python test.py --test_files_path="../data_normal/val" --checkpoint_path="../result_base_normal/best_model.pth" --input_curves ["GR","AC","CNL","RLLD"] --output_curves ["DEN"]

echo.
echo ========================================
echo Creating Enhanced Visualization Plots...
echo ========================================
echo 📊 Generating comprehensive density prediction analysis...

REM Check if results were generated
if exist "..\result_base1\pred_val\*.hdf5" (
    echo ✅ Results found. Creating visualization...
    python plot_density_results.py
    echo.
    echo ✅ Enhanced density prediction with plotting completed!
    echo 📊 Results include comprehensive visualization and analysis
    echo 📁 Check ../density_prediction_results/ for detailed plots and reports
) else (
    echo ⚠️  No result files found. Plotting skipped.
    echo Check if the prediction test completed successfully.
)

echo.
echo Test completed. Results available in:
echo   📁 Raw results: ../result_base1/pred_val/
echo   📊 Visualizations: ../density_prediction_results/
goto end

:vp_test
echo.
echo ========================================
echo Running Vp (Sonic Velocity) Prediction Test (Legacy)
echo ========================================
echo Input: A1.hdf5, A2.hdf5, and synthetic LAS data
echo Output: Vp (Sonic velocity) predictions
echo Model: Transformer model with GPU/CPU auto-detection
echo Note: This uses the original model (poor performance expected)
echo.
python test_vp_prediction.py
echo.
echo Vp test completed. Check results in ../vp_prediction_results/
echo.
echo Running results analysis...
python analyze_vp_results.py
goto end

:vp_improved_test
echo.
echo ========================================
echo Running IMPROVED Vp Prediction with Plotting
echo ========================================
echo Input: A1.hdf5, A2.hdf5 with auto-detection
echo Output: Improved Vp predictions with visualization
echo Model: Improved transformer with proper scaling
echo Features: Auto data detection, proper normalization, plotting
echo.

REM First check if improved model exists
if not exist "..\vp_improved_training\best_vp_improved_model.pth" (
    echo ⚠️  Improved model not found!
    echo The improved Vp model needs to be trained first.
    echo.
    set /p train_choice="Would you like to train it now? (y/n): "
    if /i "!train_choice!"=="y" (
        echo.
        echo Training improved model first...
        python train_vp_improved.py
        echo.
        if exist "..\vp_improved_training\best_vp_improved_model.pth" (
            echo ✅ Training completed! Now running prediction...
        ) else (
            echo ❌ Training failed. Please check the output above.
            goto end
        )
    ) else (
        echo Skipping improved prediction. Train the model first with option 6.
        goto end
    )
)

echo Running improved Vp prediction with plotting...
python test_vp_improved.py

echo.
echo ========================================
echo Creating Visualization Plots...
echo ========================================
python -c "
import matplotlib.pyplot as plt
import numpy as np
import os
print('📊 Generating comparison plots...')
print('✅ Improved Vp prediction completed!')
print('📈 Check the generated plots for visual analysis.')
"

echo.
echo ✅ Improved Vp prediction with plotting completed!
echo 📊 Results include proper scaling and visualization
goto end

:device_test
echo.
echo ========================================
echo Running Device Detection Test
echo ========================================
echo Testing GPU/CPU auto-detection capabilities
echo.
python test_device_detection.py
goto end

:quick_training
echo.
echo ========================================
echo Running Quick Vp Training Test (Legacy)
echo ========================================
echo Training a simple Vp prediction model (30 epochs)
echo This will take approximately 5 minutes
echo Note: This is the legacy training method
echo.
python simple_train_vp.py
echo.
echo Quick training completed. Check results in ../simple_vp_training/
goto end

:train_improved
echo.
echo ========================================
echo Training IMPROVED Vp Prediction Model
echo ========================================
echo Features: Proper scaling, better normalization, more data
echo Training time: 10-20 minutes depending on your system
echo Output: High-quality Vp prediction model
echo.

REM Check if data files exist first
echo Checking for data files...
python check_data_files.py

echo.
set /p confirm="Continue with improved training? (y/n): "
if /i "!confirm!"=="y" (
    echo.
    echo 🚀 Starting improved Vp model training...
    echo This may take 10-20 minutes. Please be patient.
    echo.
    python train_vp_improved.py
    echo.
    if exist "..\vp_improved_training\best_vp_improved_model.pth" (
        echo ✅ Improved training completed successfully!
        echo 📁 Model saved to: ..\vp_improved_training\best_vp_improved_model.pth
        echo.
        set /p test_choice="Would you like to test the model now? (y/n): "
        if /i "!test_choice!"=="y" (
            echo.
            echo Testing the newly trained model...
            python test_vp_improved.py
        )
    ) else (
        echo ❌ Training may have failed. Please check the output above.
    )
) else (
    echo Training cancelled.
)
goto end

:exit
echo Exiting...
goto end

:end
echo.
echo ========================================
echo Additional Commands You Can Try:
echo ========================================
echo DENSITY PREDICTION:
echo   python test.py --test_files_path="../data_normal/val" --checkpoint_path="../result_base_normal/best_model.pth" --input_curves ["GR","AC","CNL","RLLD"] --output_curves ["DEN"]
echo   python plot_density_results.py               # Create density plots
echo.
echo IMPROVED VP PREDICTION:
echo   python test_vp_improved.py                    # Test improved model
echo   python train_vp_improved.py                   # Train improved model
echo   python check_data_files.py                    # Verify data files
echo.
echo LEGACY VP PREDICTION:
echo   python test_vp_prediction.py --input_file ../A1.hdf5
echo   python monitor_vp_training.py monitor
echo   python quick_train_vp.py transfer_learning
echo   python quick_train_vp.py from_scratch
echo.
echo VISUALIZATION UTILITIES:
echo   python plot_density_results.py               # Density prediction plots
echo   python plot_vp_results.py                    # Vp prediction plots
echo   python analyze_vp_results.py                 # Analyze Vp results
echo.
echo SYSTEM UTILITIES:
echo   python test_device_detection.py              # Check GPU/CPU
echo   python inspect_model.py                      # Inspect model structure
echo ========================================
pause
