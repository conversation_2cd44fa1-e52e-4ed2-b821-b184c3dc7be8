"""
Improved Vp prediction script with proper scaling and normalization
"""
import os
import argparse
import torch
import numpy as np
import h5py
from torch.utils.data import DataLoader

from vp_model_improved import MWLT_Vp_Small, MWLT_Vp_Base, MWLT_Vp_Large, VpDataNormalizer
from utils import get_device, load_checkpoint, cal_RMSE, cal_R2
from las_processor import LASProcessor

try:
    from plot_vp_results import create_vp_comparison_plots
    PLOTTING_AVAILABLE = True
except ImportError:
    PLOTTING_AVAILABLE = False
    print("⚠️  Plotting functionality not available. Install matplotlib for enhanced visualization.")

def find_data_files():
    """
    Auto-detect the location of data files
    """
    # Possible locations to search
    possible_paths = [
        # Current directory and parent
        '.',
        '..',
        # Your specific path
        r"C:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\main\Transformer",
        # Relative paths from code directory
        os.path.join('..'),
        os.path.join('..', '..'),
    ]

    found_files = []
    for base_path in possible_paths:
        a1_path = os.path.join(base_path, 'A1.hdf5')
        a2_path = os.path.join(base_path, 'A2.hdf5')
        well_path = os.path.join(base_path, 'las_test_data', 'WELL_001.hdf5')

        if os.path.exists(a1_path) and os.path.exists(a2_path):
            found_files = [a1_path, a2_path]
            if os.path.exists(well_path):
                found_files.append(well_path)
            print(f"✅ Found data files in: {os.path.abspath(base_path)}")
            break

    return found_files

def predict_vp_improved(model_path, input_files=None, device=0):
    """
    Improved Vp prediction with proper scaling
    """
    device = get_device(device)
    normalizer = VpDataNormalizer()
    processor = LASProcessor()
    
    # Auto-detect input files if not provided
    if input_files is None:
        input_files = find_data_files()
        if not input_files:
            print("❌ No data files found! Please check file locations.")
            return []
    
    # Load improved model
    print(f"Loading improved Vp model from {model_path}")
    model = MWLT_Vp_Base()  # Adjust based on your trained model
    model = model.to(device)
    
    try:
        checkpoint = torch.load(model_path, map_location=device)
        if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        else:
            model.load_state_dict(checkpoint)
        model.eval()
        print("✅ Model loaded successfully")
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return
    
    results = []
    
    for file_path in input_files:
        if not os.path.exists(file_path):
            print(f"Warning: {file_path} not found, skipping...")
            continue
            
        print(f"\nProcessing {file_path}...")
        
        # Load and process data
        curves = processor.process_hdf5_to_las_format(file_path)
        
        # Prepare input features with proper normalization
        input_features = []
        for curve_name in ['GR', 'CNL', 'DEN', 'RLLD']:
            if curve_name in curves:
                data = torch.FloatTensor(curves[curve_name])
                if curve_name == 'RLLD':
                    # Log transform for resistivity
                    data = torch.log10(torch.clamp(data, min=0.1))
                    normalized = (data - 1.0) / 2.0
                else:
                    stats = normalizer.input_stats[curve_name]
                    normalized = (data - stats['mean']) / stats['std']
                    normalized = torch.clamp(normalized, -3, 3) / 3
                input_features.append(normalized)
            else:
                print(f"Warning: {curve_name} not found, using zeros")
                input_features.append(torch.zeros(len(curves['AC'])))
        
        # Stack and prepare for model
        input_tensor = torch.stack(input_features).unsqueeze(0).to(device)  # [1, 4, seq_len]
        
        # Ensure correct sequence length
        target_length = 640
        if input_tensor.shape[2] > target_length:
            input_tensor = input_tensor[:, :, :target_length]
        elif input_tensor.shape[2] < target_length:
            # Pad if necessary
            padding = target_length - input_tensor.shape[2]
            input_tensor = torch.nn.functional.pad(input_tensor, (0, padding))
        
        # Predict
        with torch.no_grad():
            prediction = model(input_tensor)
            prediction = prediction.cpu().numpy().flatten()
        
        # Get actual values for comparison
        actual_vp = curves.get('AC', np.zeros_like(prediction))
        if len(actual_vp) > len(prediction):
            actual_vp = actual_vp[:len(prediction)]
        elif len(actual_vp) < len(prediction):
            prediction = prediction[:len(actual_vp)]
        
        # Calculate metrics
        rmse = cal_RMSE(prediction, actual_vp)
        r2 = cal_R2(prediction, actual_vp)
        
        result = {
            'file': os.path.basename(file_path),
            'rmse': rmse,
            'r2': r2,
            'pred_range': [prediction.min(), prediction.max()],
            'actual_range': [actual_vp.min(), actual_vp.max()],
            'prediction': prediction,
            'actual': actual_vp
        }
        results.append(result)
        
        print(f"  RMSE: {rmse:.2f}")
        print(f"  R²: {r2:.4f}")
        print(f"  Predicted range: [{prediction.min():.2f}, {prediction.max():.2f}]")
        print(f"  Actual range: [{actual_vp.min():.2f}, {actual_vp.max():.2f}]")
    
    return results

def main():
    parser = argparse.ArgumentParser(description="Improved Vp prediction")
    parser.add_argument("--model_path", type=str,
                       default=os.path.join('..', 'vp_improved_training', 'best_vp_improved_model.pth'),
                       help="Path to improved Vp model")
    parser.add_argument("--device", type=int, default=0, help="GPU device ID")
    parser.add_argument("--input_files", nargs='+', default=None,
                       help="Input files to process")
    
    args = parser.parse_args()
    
    print("=== Improved Vp Prediction Test ===")
    
    if not os.path.exists(args.model_path):
        print(f"❌ Model not found: {args.model_path}")
        print("Please train the improved model first:")
        print("  python train_vp_improved.py")
        return
    
    results = predict_vp_improved(args.model_path, args.input_files, args.device)
    
    if results:
        print("\n=== Summary ===")
        avg_rmse = np.mean([r['rmse'] for r in results])
        avg_r2 = np.mean([r['r2'] for r in results])
        print(f"Average RMSE: {avg_rmse:.2f}")
        print(f"Average R²: {avg_r2:.4f}")

        # Create enhanced plots
        if PLOTTING_AVAILABLE:
            print("\n=== Creating Enhanced Visualizations ===")
            try:
                plot_file = create_vp_comparison_plots(results)
                print(f"✅ Enhanced plots created successfully!")
                print(f"📊 Main plot: {plot_file}")
            except Exception as e:
                print(f"⚠️  Error creating plots: {e}")
                print("Continuing without enhanced visualization...")
        else:
            print("\n⚠️  Enhanced plotting not available. Install matplotlib for better visualization.")

        print("\n=== Performance Assessment ===")
        if avg_rmse < 50 and avg_r2 > 0.8:
            print("🎉 EXCELLENT performance! Model is working very well.")
        elif avg_rmse < 100 and avg_r2 > 0.5:
            print("✅ GOOD performance! Model shows promising results.")
        else:
            print("⚠️  Performance needs improvement. Consider:")
            print("  - More training data")
            print("  - Longer training (more epochs)")
            print("  - Different model architecture")
            print("  - Better data preprocessing")

        print(f"\n📈 Results saved with enhanced visualization capabilities")
        print(f"🔍 Check the generated plots for detailed analysis")

if __name__ == "__main__":
    main()
