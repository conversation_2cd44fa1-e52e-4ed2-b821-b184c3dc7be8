"""
Simple script to verify data file locations
"""
import os
import h5py

def check_data_files():
    """
    Check if A1.hdf5 and A2.hdf5 files exist and are readable
    """
    print("=== Data File Location Checker ===\n")
    
    # Possible locations to search
    possible_paths = [
        # Current directory and parent
        '.',
        '..',
        # Your specific path
        r"C:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\main\Transformer",
        # Relative paths from code directory
        os.path.join('..'),
        os.path.join('..', '..'),
    ]
    
    print("Searching for A1.hdf5 and A2.hdf5 in:")
    for i, path in enumerate(possible_paths, 1):
        abs_path = os.path.abspath(path)
        print(f"{i}. {abs_path}")
    
    print("\n" + "="*60)
    
    found_files = []
    for i, base_path in enumerate(possible_paths, 1):
        abs_path = os.path.abspath(base_path)
        a1_path = os.path.join(base_path, 'A1.hdf5')
        a2_path = os.path.join(base_path, 'A2.hdf5')
        
        print(f"\n{i}. Checking: {abs_path}")
        
        a1_exists = os.path.exists(a1_path)
        a2_exists = os.path.exists(a2_path)
        
        print(f"   A1.hdf5: {'✅ Found' if a1_exists else '❌ Not found'}")
        print(f"   A2.hdf5: {'✅ Found' if a2_exists else '❌ Not found'}")
        
        if a1_exists and a2_exists:
            print(f"   🎉 Both files found!")
            found_files = [a1_path, a2_path]
            
            # Try to read the files
            try:
                print("   📖 Testing file readability...")
                with h5py.File(a1_path, 'r') as f:
                    curves_a1 = list(f.keys())
                    print(f"   A1.hdf5 curves: {curves_a1}")
                
                with h5py.File(a2_path, 'r') as f:
                    curves_a2 = list(f.keys())
                    print(f"   A2.hdf5 curves: {curves_a2}")
                
                print("   ✅ Files are readable!")
                break
                
            except Exception as e:
                print(f"   ❌ Error reading files: {e}")
    
    print("\n" + "="*60)
    
    if found_files:
        print("🎉 SUCCESS: Data files found and readable!")
        print(f"Location: {os.path.dirname(os.path.abspath(found_files[0]))}")
        print("\nYou can now run:")
        print("  python train_vp_improved.py")
        print("  python test_vp_improved.py")
    else:
        print("❌ PROBLEM: Could not find both A1.hdf5 and A2.hdf5 files!")
        print("\nPlease ensure both files are in one of the searched locations.")
        print("Current working directory:", os.getcwd())
        
        # List files in current directory
        print("\nFiles in current directory:")
        try:
            files = os.listdir('.')
            hdf5_files = [f for f in files if f.endswith('.hdf5')]
            if hdf5_files:
                for f in hdf5_files:
                    print(f"  - {f}")
            else:
                print("  No .hdf5 files found")
        except Exception as e:
            print(f"  Error listing files: {e}")
        
        # List files in parent directory
        print("\nFiles in parent directory:")
        try:
            files = os.listdir('..')
            hdf5_files = [f for f in files if f.endswith('.hdf5')]
            if hdf5_files:
                for f in hdf5_files:
                    print(f"  - {f}")
            else:
                print("  No .hdf5 files found")
        except Exception as e:
            print(f"  Error listing files: {e}")

if __name__ == "__main__":
    check_data_files()
