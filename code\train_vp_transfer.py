"""
Transfer Learning for Vp Prediction
Uses pre-trained density prediction model as starting point for sonic velocity prediction
"""
import os
import argparse
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import time

from utils import get_device, load_checkpoint, save_checkpoint, EarlyStopping, cal_RMSE, cal_R2
from model import MWLT_Small, MWLT_Base, MWLT_Large
from dataset import WellDataset
from train_vp_model import create_vp_training_data

def adapt_model_for_vp(model, freeze_encoder=True):
    """
    Adapt a pre-trained density model for Vp prediction
    
    Args:
        model: Pre-trained model
        freeze_encoder: Whether to freeze encoder layers for transfer learning
    
    Returns:
        Adapted model
    """
    print("=== Adapting Model for Vp Prediction ===")
    
    # Count parameters before adaptation
    total_params_before = sum(p.numel() for p in model.parameters())
    trainable_params_before = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    if freeze_encoder:
        # Freeze encoder layers (feature_embedding and transformer_encoder)
        for name, param in model.named_parameters():
            if 'feature_embedding' in name or 'transformer_encdoer' in name or 'position_embedding' in name:
                param.requires_grad = False
                print(f"  Frozen: {name}")
        
        print("  Encoder layers frozen - only decoder will be fine-tuned")
    else:
        print("  All layers will be fine-tuned")
    
    # The decoder output layer might need adjustment if the output activation is different
    # For Vp prediction, we might want different activation than density prediction
    
    # Count parameters after adaptation
    trainable_params_after = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"  Total parameters: {total_params_before:,}")
    print(f"  Trainable before: {trainable_params_before:,}")
    print(f"  Trainable after: {trainable_params_after:,}")
    print(f"  Frozen parameters: {total_params_before - trainable_params_after:,}")
    
    return model

def train_vp_transfer_learning(args):
    """
    Train Vp prediction model using transfer learning from density model
    """
    print("=== Vp Transfer Learning Training ===")
    
    # Device setup
    device = get_device(int(args.device))
    
    # Create save directory
    if not os.path.exists(args.save_path):
        os.makedirs(args.save_path)
    
    # Create training data if needed
    if not os.path.exists(args.train_files_path) or not os.path.exists(args.val_files_path):
        print("Creating Vp training data...")
        train_dir, val_dir = create_vp_training_data()
        args.train_files_path = train_dir
        args.val_files_path = val_dir
    
    # Prepare datasets
    train_dataset = WellDataset(
        root_path=args.train_files_path,
        input_curves=args.input_curves,
        output_curves=args.output_curves,
        transform=args.transform,
        total_seqlen=args.total_seqlen,
        effect_seqlen=args.effect_seqlen
    )
    
    val_dataset = WellDataset(
        root_path=args.val_files_path,
        input_curves=args.input_curves,
        output_curves=args.output_curves,
        transform=False,
        total_seqlen=args.total_seqlen,
        effect_seqlen=args.effect_seqlen
    )
    
    train_loader = DataLoader(dataset=train_dataset, batch_size=args.batch_size, shuffle=True)
    val_loader = DataLoader(dataset=val_dataset, batch_size=1, shuffle=False)
    
    print(f"Training samples: {len(train_dataset)}")
    print(f"Validation samples: {len(val_dataset)}")
    
    # Load pre-trained model
    print(f"\nLoading pre-trained model: {args.pretrained_model_path}")
    
    # Create model architecture
    if args.model_type == "small":
        model = MWLT_Small(
            in_channels=len(args.input_curves),
            out_channels=len(args.output_curves),
            feature_num=args.feature_num,
            use_pe=args.use_pe,
            drop=args.drop,
            attn_drop=args.attn_drop,
            position_drop=args.position_drop
        )
    elif args.model_type == "base":
        model = MWLT_Base(
            in_channels=len(args.input_curves),
            out_channels=len(args.output_curves),
            feature_num=args.feature_num,
            use_pe=args.use_pe,
            drop=args.drop,
            attn_drop=args.attn_drop,
            position_drop=args.position_drop
        )
    elif args.model_type == "large":
        model = MWLT_Large(
            in_channels=len(args.input_curves),
            out_channels=len(args.output_curves),
            feature_num=args.feature_num,
            use_pe=args.use_pe,
            drop=args.drop,
            attn_drop=args.attn_drop,
            position_drop=args.position_drop
        )
    
    model = model.to(device)
    
    # Load pre-trained weights
    try:
        model_dict, epoch, loss = load_checkpoint(args.pretrained_model_path, device)
        
        # Try to load compatible layers
        model_state = model.state_dict()
        pretrained_dict = {}
        
        for k, v in model_dict.items():
            if k in model_state and model_state[k].shape == v.shape:
                pretrained_dict[k] = v
                print(f"  Loaded: {k}")
            else:
                print(f"  Skipped: {k} (shape mismatch or not found)")
        
        model_state.update(pretrained_dict)
        model.load_state_dict(model_state)
        
        print(f"Successfully loaded {len(pretrained_dict)}/{len(model_dict)} layers")
        
    except Exception as e:
        print(f"Warning: Could not load pre-trained model: {e}")
        print("Training from scratch...")
    
    # Adapt model for transfer learning
    model = adapt_model_for_vp(model, freeze_encoder=args.freeze_encoder)
    
    # Setup optimizer with different learning rates for frozen/unfrozen layers
    if args.freeze_encoder:
        # Lower learning rate for fine-tuning
        optimizer = optim.Adam(
            filter(lambda p: p.requires_grad, model.parameters()),
            lr=args.learning_rate * 0.1  # 10x lower learning rate for fine-tuning
        )
        print(f"Using reduced learning rate: {args.learning_rate * 0.1}")
    else:
        optimizer = optim.Adam(model.parameters(), lr=args.learning_rate)
    
    criterion = nn.MSELoss().to(device)
    
    # Early stopping
    early_stopping = EarlyStopping(
        patience=args.patience,
        path=os.path.join(args.save_path, "best_vp_transfer_model.pth"),
        verbose=True
    )
    
    # Training loop
    print(f"\nStarting transfer learning...")
    print(f"  Freeze encoder: {args.freeze_encoder}")
    print(f"  Learning rate: {args.learning_rate}")
    print(f"  Epochs: {args.epochs}")
    print("-" * 80)
    
    training_log = {"epoch": [], "train_loss": [], "val_loss": [], "val_rmse": [], "val_r2": []}
    
    for epoch in range(1, args.epochs + 1):
        epoch_start_time = time.time()
        
        # Training phase
        model.train()
        train_total_loss = 0.0
        
        for step, (conditions, targets) in enumerate(train_loader):
            conditions = conditions.to(device)
            targets = targets.to(device)
            
            predictions = model(conditions)
            loss = criterion(predictions, targets)
            
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            train_total_loss += loss.item()
        
        train_loss_epoch = train_total_loss / len(train_loader)
        
        # Validation phase
        model.eval()
        val_total_loss = 0.0
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for conditions, targets in val_loader:
                conditions = conditions.to(device)
                targets = targets.to(device)
                
                predictions = model(conditions)
                loss = criterion(predictions, targets)
                val_total_loss += loss.item()
                
                all_predictions.append(predictions.cpu().numpy())
                all_targets.append(targets.cpu().numpy())
        
        val_loss_epoch = val_total_loss / len(val_loader)
        
        # Calculate metrics
        all_predictions = np.concatenate(all_predictions, axis=0).flatten()
        all_targets = np.concatenate(all_targets, axis=0).flatten()
        val_rmse = cal_RMSE(all_predictions, all_targets)
        val_r2 = cal_R2(all_predictions, all_targets)
        
        # Log results
        training_log["epoch"].append(epoch)
        training_log["train_loss"].append(train_loss_epoch)
        training_log["val_loss"].append(val_loss_epoch)
        training_log["val_rmse"].append(val_rmse)
        training_log["val_r2"].append(val_r2)
        
        # Print progress
        epoch_time = time.time() - epoch_start_time
        print(f"Epoch [{epoch:4d}/{args.epochs}] | "
              f"Train Loss: {train_loss_epoch:.6f} | "
              f"Val Loss: {val_loss_epoch:.6f} | "
              f"Val RMSE: {val_rmse:.4f} | "
              f"Val R²: {val_r2:.4f} | "
              f"Time: {epoch_time:.2f}s")
        
        # Save checkpoint and check early stopping
        state = {
            "model_state_dict": model.state_dict(),
            "optimizer_state_dict": optimizer.state_dict(),
            "loss": val_loss_epoch,
            "epoch": epoch,
            "rmse": val_rmse,
            "r2": val_r2,
            "transfer_learning": True,
            "freeze_encoder": args.freeze_encoder
        }
        
        early_stopping(state, model)
        
        if early_stopping.early_stop:
            print(f"Early stopping triggered at epoch {epoch}")
            break
    
    print("-" * 80)
    print("Transfer learning completed!")
    print(f"Best model saved to: {os.path.join(args.save_path, 'best_vp_transfer_model.pth')}")

def main():
    parser = argparse.ArgumentParser(description="Transfer learning for Vp prediction")
    
    # Transfer learning specific
    parser.add_argument("--pretrained_model_path", type=str, 
                       default="../result_base_normal/best_model.pth",
                       help="Path to pre-trained density model")
    parser.add_argument("--freeze_encoder", type=bool, default=True,
                       help="Freeze encoder layers for transfer learning")
    
    # Model configuration
    parser.add_argument("--model_type", type=str, default="base", choices=["small", "base", "large"])
    parser.add_argument("--feature_num", type=int, default=64)
    parser.add_argument("--use_pe", type=bool, default=True)
    parser.add_argument("--drop", type=float, default=0.1)
    parser.add_argument("--attn_drop", type=float, default=0.1)
    parser.add_argument("--position_drop", type=float, default=0.1)
    
    # Data configuration for Vp prediction
    parser.add_argument("--input_curves", default=["GR", "CNL", "DEN", "RLLD"], type=list)
    parser.add_argument("--output_curves", default=["AC"], type=list)
    parser.add_argument("--total_seqlen", default=720, type=int)
    parser.add_argument("--effect_seqlen", default=640, type=int)
    parser.add_argument("--transform", default=True, type=bool)
    
    # Training configuration
    parser.add_argument("--batch_size", type=int, default=8, help="Smaller batch for fine-tuning")
    parser.add_argument("--learning_rate", type=float, default=1e-4)
    parser.add_argument("--epochs", type=int, default=200, help="Fewer epochs for transfer learning")
    parser.add_argument("--patience", type=int, default=50)
    
    # Paths
    parser.add_argument("--train_files_path", default="../data_vp_training/train", type=str)
    parser.add_argument("--val_files_path", default="../data_vp_training/val", type=str)
    parser.add_argument("--save_path", type=str, default="../vp_transfer_learning")
    parser.add_argument("--device", type=str, default="0")
    
    args = parser.parse_args()
    train_vp_transfer_learning(args)

if __name__ == "__main__":
    main()
