"""
Quick training script for Vp prediction with optimized configurations
Provides easy-to-use presets for different training scenarios
"""
import os
import subprocess
import arg<PERSON><PERSON>

def run_training(config_name, custom_args=None):
    """
    Run training with predefined configurations
    """
    
    configs = {
        "quick_test": {
            "model_type": "small",
            "batch_size": 8,
            "epochs": 50,
            "learning_rate": 1e-3,
            "patience": 20,
            "save_path": "../vp_quick_test"
        },
        
        "from_scratch": {
            "model_type": "base",
            "batch_size": 16,
            "epochs": 500,
            "learning_rate": 1e-4,
            "patience": 100,
            "save_path": "../vp_from_scratch"
        },
        
        "transfer_learning": {
            "script": "train_vp_transfer.py",
            "model_type": "base",
            "batch_size": 8,
            "epochs": 200,
            "learning_rate": 1e-4,
            "patience": 50,
            "freeze_encoder": True,
            "pretrained_model_path": "../result_base_normal/best_model.pth",
            "save_path": "../vp_transfer"
        },
        
        "large_model": {
            "model_type": "large",
            "batch_size": 4,
            "epochs": 300,
            "learning_rate": 5e-5,
            "patience": 80,
            "feature_num": 128,
            "save_path": "../vp_large_model"
        },
        
        "fine_tune": {
            "script": "train_vp_transfer.py",
            "model_type": "base",
            "batch_size": 4,
            "epochs": 100,
            "learning_rate": 1e-5,
            "patience": 30,
            "freeze_encoder": False,
            "pretrained_model_path": "../result_base_normal/best_model.pth",
            "save_path": "../vp_fine_tune"
        }
    }
    
    if config_name not in configs:
        print(f"Available configurations: {list(configs.keys())}")
        return
    
    config = configs[config_name]
    script = config.get("script", "train_vp_model.py")
    
    # Build command
    cmd = ["python", script]
    
    for key, value in config.items():
        if key == "script":
            continue
        cmd.extend([f"--{key}", str(value)])
    
    # Add custom arguments
    if custom_args:
        cmd.extend(custom_args)
    
    print(f"=== Running {config_name} configuration ===")
    print(f"Command: {' '.join(cmd)}")
    print("-" * 60)
    
    # Run the training
    try:
        subprocess.run(cmd, check=True)
        print(f"\n✅ {config_name} training completed successfully!")
    except subprocess.CalledProcessError as e:
        print(f"\n❌ {config_name} training failed with error: {e}")
    except KeyboardInterrupt:
        print(f"\n⏹️ {config_name} training interrupted by user")

def main():
    parser = argparse.ArgumentParser(description="Quick Vp training configurations")
    parser.add_argument("config", choices=["quick_test", "from_scratch", "transfer_learning", 
                                          "large_model", "fine_tune", "list"],
                       help="Training configuration to use")
    parser.add_argument("--device", type=str, default="0", help="GPU device")
    parser.add_argument("--custom_args", nargs="*", help="Additional arguments")
    
    args = parser.parse_args()
    
    if args.config == "list":
        print("Available training configurations:")
        print("  quick_test      - Fast test with small model (50 epochs)")
        print("  from_scratch    - Full training from scratch (500 epochs)")
        print("  transfer_learning - Transfer from density model (200 epochs)")
        print("  large_model     - Large model training (300 epochs)")
        print("  fine_tune       - Fine-tune all layers (100 epochs)")
        return
    
    # Add device to custom args
    custom_args = args.custom_args or []
    custom_args.extend(["--device", args.device])
    
    run_training(args.config, custom_args)

if __name__ == "__main__":
    main()
