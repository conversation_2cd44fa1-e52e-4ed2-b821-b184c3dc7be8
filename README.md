# MWLT - Machine Learning Well Log Transformer

A transformer-based deep learning system for predicting well log properties, specifically designed for density (DEN) and sonic velocity (Vp/AC) prediction from input well log curves.

## 🎯 Project Overview

The Machine Learning Well Log Transformer (MWLT) uses transformer architecture combined with ResNet-based feature extraction to predict missing or poor-quality well log curves from available measurements. The system supports:

- **Primary Task**: Density (DEN) prediction from GR, AC, CNL, RLLD curves
- **Secondary Task**: Sonic velocity (Vp/AC) prediction with improved architecture
- **GPU/CPU Auto-detection**: Automatic hardware optimization
- **Multiple Model Sizes**: Small, Base, and Large variants

## 📁 Codebase Structure

```
Transformer/
├── code/                           # Main source code directory
│   ├── model.py                    # Core transformer architecture
│   ├── train.py                    # Main density prediction training
│   ├── test.py                     # Model evaluation and inference
│   ├── dataset.py                  # Data loading and preprocessing
│   ├── utils.py                    # Utilities (device detection, early stopping)
│   ├── las_processor.py            # LAS file processing utilities
│   │
│   ├── vp_model_improved.py        # 🆕 Complete Vp prediction module
│   ├── train_vp_improved.py        # 🆕 Improved Vp training script
│   ├── test_vp_improved.py         # 🆕 Improved Vp testing
│   │
│   ├── run_test.bat                # 🎮 Interactive test suite
│   └── [legacy vp scripts...]      # Original Vp prediction attempts
│
├── data_normal/                    # Normalized training data
│   ├── train/                      # Training HDF5 files
│   └── val/                        # Validation HDF5 files
│
├── data_nonormal/                  # Non-normalized training data
├── data_vp_prediction/             # Vp-specific training data
├── las_test_data/                  # Sample LAS and HDF5 files
│
├── result_base_normal/             # Base model training results
├── vp_improved_training/           # 🆕 Improved Vp model results
├── simple_vp_training/             # Legacy Vp training results
│
├── A1.hdf5, A2.hdf5               # Main well log data files
└── README.md                       # This file
```

## 🔬 ML Model Architecture

### Core Components

#### 1. Input Embedding (`Input_Embedding`)
- **ResNet-based feature extraction** using 1D convolutions
- Converts raw well log curves to high-dimensional features
- Architecture: `[B, C, L] → [B, feature_num, L]`

#### 2. Positional Encoding (`PositionalEncoding`)  
- Sinusoidal position embeddings for sequence awareness
- Handles variable sequence lengths (up to 720 points)
- Format: `[B, L, feature_num]`

#### 3. Transformer Encoder (`TransformerBlock`)
- Multi-head self-attention mechanism
- Layer normalization and residual connections
- Configurable depth and attention heads

#### 4. Decoder
- **Standard Decoder**: For density prediction with sigmoid activation
- **VpDecoder**: 🆕 Specialized for sonic velocity with proper scaling (40-400 μs/ft)

### Model Variants

| Model | ResNet Blocks | Transformer Layers | Attention Heads | Features |
|-------|---------------|-------------------|-----------------|----------|
| **Small** | 2 | 2 | 2 | 64 |
| **Base** | 4 | 4 | 4 | 64 |
| **Large** | 6 | 6 | 8 | 128 |

### Data Flow

```
Input Curves [B,4,720] 
    ↓ Input_Embedding
Feature Maps [B,64,720]
    ↓ Transpose
Transformer Input [B,720,64]
    ↓ PositionalEncoding + TransformerBlock  
Context Features [B,720,64]
    ↓ Transpose
CNN Format [B,64,720]
    ↓ Decoder
Output Curves [B,1,720]
```

## 📊 Dataset Format

### Input Curves (Features)
- **GR**: Gamma Ray (radioactivity, shale content) - API units
- **AC**: Acoustic/Sonic (travel time, porosity) - μs/ft  
- **CNL**: Neutron (hydrogen content, porosity) - %
- **RLLD**: Deep Resistivity (fluid saturation) - ohm⋅m

### Output Curves (Targets)
- **DEN**: Bulk Density - g/cm³ (Primary task)
- **AC**: Acoustic/Sonic - μs/ft (Vp prediction task)

### Data Format
- **File Type**: HDF5 (`.hdf5`) or LAS (`.las`)
- **Sequence Length**: 720 points → 640 effective (with augmentation)
- **Normalization**: Curve-specific with statistical and physical constraints
- **Augmentation**: Sliding windows with 50% overlap

### Expected Data Files
- `A1.hdf5`: Primary well log dataset
- `A2.hdf5`: Secondary well log dataset
- Training data in `data_normal/train/` and `data_normal/val/`

## 🚀 Getting Started

### Prerequisites
```bash
pip install torch torchvision numpy pandas h5py scikit-learn matplotlib
pip install thop  # For model profiling
```

### Quick Start Guide

#### 1. Interactive Test Suite (Recommended)
```bash
cd code/
run_test.bat  # Windows
```

**Available Options:**
- **Option 1**: Standard density prediction test
- **Option 2**: Legacy Vp prediction (poor performance)  
- **Option 3**: 🆕 **Improved Vp prediction with plotting** (Recommended)
- **Option 4**: Device detection test
- **Option 5**: Quick Vp training (legacy)
- **Option 6**: 🆕 **Train improved Vp model** (Recommended)

#### 2. Manual Training

**For Density Prediction:**
```bash
cd code/
python train.py --model_type base --epochs 2000 --batch_size 64 --save_path ../result_base_normal
```

**For Improved Vp Prediction:**
```bash
cd code/
python train_vp_improved.py  # Uses improved architecture
# or
python vp_model_improved.py --mode train --model_type base
```

#### 3. Testing Models

**Density Prediction:**
```bash
python test.py --test_files_path="../data_normal/val" --checkpoint_path="../result_base_normal/best_model.pth"
```

**Improved Vp Prediction:**
```bash
python test_vp_improved.py  # Automatic data detection
# or  
python vp_model_improved.py --mode predict --model_path model.pth --input_file A1.hdf5
```

### Key Entry Points

| Task | Recommended Script | Description |
|------|-------------------|-------------|
| **Start Here** | `run_test.bat` | Interactive test suite |
| **Density Training** | `train.py` | Main density prediction |
| **Vp Training** | `train_vp_improved.py` | 🆕 Improved Vp model |
| **Evaluation** | `test.py` / `test_vp_improved.py` | Model testing |
| **All-in-One** | `vp_model_improved.py` | Complete Vp pipeline |

## ⚙️ Configuration

### Training Parameters

**Density Prediction:**
- Learning Rate: `1e-5`
- Batch Size: `64`
- Epochs: `2000`
- Patience: `150`

**Vp Prediction (Improved):**
- Learning Rate: `1e-4`  
- Batch Size: `8`
- Epochs: `200`
- Patience: `50`

### Hardware Requirements
- **GPU**: CUDA-compatible (recommended, 4-5x faster)
- **CPU**: Fallback mode (functional but slower)
- **Memory**: 8GB+ RAM, 4GB+ GPU memory
- **Storage**: 2GB+ for datasets and models

## 🔧 Key Features

### Auto-Detection Systems
- **Hardware**: Automatic GPU/CPU selection with device info
- **Data Files**: Auto-discovery of A1.hdf5/A2.hdf5 in multiple locations
- **Model Loading**: Device-aware checkpoint loading

### Improved Vp Architecture
- **Proper Scaling**: Output constrained to realistic Vp range (40-400 μs/ft)
- **Better Normalization**: Curve-specific normalization including log-transform for resistivity
- **Data Augmentation**: Overlapping windows for more training samples
- **Custom Loss**: Physics-aware loss function with constraint penalties

### Quality Assurance
- **Early Stopping**: Prevents overfitting with patience-based monitoring
- **Metrics Tracking**: RMSE, R², and training/validation curves
- **Checkpointing**: Automatic best model saving

## 📈 Performance Expectations

### Density Prediction
- **RMSE**: <0.1 g/cm³ on normalized data
- **R²**: >0.9 on validation set
- **Training Time**: 2-4 hours (GPU), 8-12 hours (CPU)

### Vp Prediction (Improved)
- **RMSE**: Significantly improved over legacy models
- **Range**: Properly constrained to 40-400 μs/ft
- **Training Time**: 10-20 minutes (GPU), 1-2 hours (CPU)

## 🛠️ Development Notes

### Testing Framework
- No standardized test framework - uses custom evaluation scripts
- Performance monitoring through training loss curves and validation metrics
- Visual inspection through plotting utilities

### Data Pipeline
- Supports both HDF5 and LAS file formats
- Automatic curve name standardization
- Handles missing data with zero-padding
- Quality control through statistical validation

### Model Saving Format
```python
checkpoint = {
    "model_state_dict": model.state_dict(),
    "optimizer_state_dict": optimizer.state_dict(), 
    "loss": validation_loss,
    "epoch": current_epoch,
    "rmse": validation_rmse,
    "r2": validation_r2
}
```

## 🚨 Troubleshooting

### Common Issues

**1. "No module named 'thop'"**
```bash
pip install thop
```

**2. "Could not find A1.hdf5 and A2.hdf5"**
- Ensure data files are in the main Transformer directory
- Check the auto-detection paths in console output

**3. "CUDA out of memory"**
- Reduce batch size: `--batch_size 4` or `--batch_size 2`
- Use CPU mode if necessary

**4. "Tensor dimension mismatch"**
- This has been fixed in the improved models
- Use `vp_model_improved.py` instead of legacy scripts

### Performance Issues
- **Poor Vp predictions**: Use the improved model (`train_vp_improved.py`)
- **Slow training**: Check GPU availability with device detection test
- **Poor convergence**: Verify data normalization and learning rate

## 📚 References

### Well Log Domain Context
- **GR**: Shale content indicator (0-200 API)
- **AC**: Porosity/lithology indicator (40-400 μs/ft)
- **CNL**: Porosity indicator (0-60%)
- **RLLD**: Fluid saturation indicator (0.1-1000 ohm⋅m)
- **DEN**: Lithology/porosity indicator (1.5-3.0 g/cm³)

### Model Applications
- Fill missing log sections
- Quality control and validation
- Synthetic log generation
- Real-time logging optimization

---

**Getting Started**: Run `code/run_test.bat` and select Option 6 to train the improved Vp model, then Option 3 to test it with visualization!